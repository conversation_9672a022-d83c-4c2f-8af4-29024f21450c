




(index):64 cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation
(anonymous) @ (index):64
(anonymous) @ (index):64
client:789 [vite] connecting...
client:912 [vite] connected.
react-dom_client.js?v=56a8fafc:17995 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
App.tsx:2375 🎵 CISCO: État audio changé → DÉSACTIVÉ
App.tsx:2375 🎵 CISCO: État audio changé → DÉSACTIVÉ
[Violation] Forced reflow while executing JavaScript took 32ms
[Violation] Forced reflow while executing JavaScript took 34ms
client:883 [vite] hot updated: /Components/UI/SlideFooter.tsx
client:883 [vite] hot updated: /Components/UI/SlideFooter.tsx
App.tsx:2375 🎵 CISCO: État audio changé → ACTIVÉ
AmbientSoundManagerV2.tsx:269 🎵 Fade in du son 1: hibou-molkom.mp3
AmbientSoundManagerV2.tsx:302 🦉 Temporisation hibou activée - répétition toutes les 3-4 minutes
AmbientSoundManagerV2.tsx:269 🎵 Fade in du son 2: night-atmosphere-with-crickets-374652.mp3
AmbientSoundManagerV2.tsx:269 🎵 Fade in du son 3: sounds-crickets-nuit_profonde.mp3





























































