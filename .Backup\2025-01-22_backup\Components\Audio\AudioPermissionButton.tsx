import React, { useState, useCallback } from 'react';

// 🔧 CISCO: Composant pour débloquer l'audio après interaction utilisateur
interface AudioPermissionButtonProps {
  onAudioUnlocked?: () => void;
  className?: string;
}

const AudioPermissionButton: React.FC<AudioPermissionButtonProps> = ({ 
  onAudioUnlocked, 
  className = '' 
}) => {
  const [isUnlocked, setIsUnlocked] = useState(false);
  const [isUnlocking, setIsUnlocking] = useState(false);

  const unlockAudio = useCallback(async () => {
    if (isUnlocked || isUnlocking) return;

    setIsUnlocking(true);
    console.log('🔓 Tentative de déblocage audio...');

    try {
      // Créer un contexte audio et le démarrer
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      if (audioContext.state === 'suspended') {
        await audioContext.resume();
        console.log('✅ Contexte audio repris');
      }

      // Jouer un son silencieux pour débloquer l'audio
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      gainNode.gain.value = 0; // Volume à 0 (silencieux)
      oscillator.frequency.value = 440; // Note A4
      
      oscillator.start();
      oscillator.stop(audioContext.currentTime + 0.1); // Jouer pendant 0.1 seconde
      
      // Fermer le contexte de test
      setTimeout(async () => {
        await audioContext.close();
        console.log('✅ Audio débloqué avec succès');
        setIsUnlocked(true);
        setIsUnlocking(false);
        
        if (onAudioUnlocked) {
          onAudioUnlocked();
        }
      }, 200);

    } catch (error) {
      console.error('❌ Erreur lors du déblocage audio:', error);
      setIsUnlocking(false);
      
      // Même en cas d'erreur, considérer que l'interaction a eu lieu
      setIsUnlocked(true);
      if (onAudioUnlocked) {
        onAudioUnlocked();
      }
    }
  }, [isUnlocked, isUnlocking, onAudioUnlocked]);

  if (isUnlocked) {
    return (
      <div className={`bg-green-800/90 backdrop-blur-sm rounded-lg p-3 border border-green-600 ${className}`}>
        <div className="flex items-center gap-2 text-green-300">
          <span className="text-lg">✅</span>
          <span className="text-sm font-medium">Audio débloqué</span>
        </div>
        <p className="text-xs text-green-400 mt-1">
          Les sons d'ambiance peuvent maintenant être joués
        </p>
      </div>
    );
  }

  return (
    <div className={`bg-orange-800/90 backdrop-blur-sm rounded-lg p-3 border border-orange-600 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <span className="text-lg">🔒</span>
          <span className="text-sm font-medium text-orange-300">Audio bloqué</span>
        </div>
      </div>
      
      <p className="text-xs text-orange-200 mb-3">
        Les navigateurs bloquent l'audio automatique. Cliquez pour débloquer les sons d'ambiance.
      </p>
      
      <button
        onClick={unlockAudio}
        disabled={isUnlocking}
        className={`w-full px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
          isUnlocking
            ? 'bg-orange-600 text-white cursor-not-allowed'
            : 'bg-orange-600 hover:bg-orange-700 text-white'
        }`}
      >
        {isUnlocking ? '🔄 Déblocage...' : '🔓 Débloquer Audio'}
      </button>
    </div>
  );
};

export default AudioPermissionButton;
