import React, { useState, useRef } from 'react';
import CinemaController from './CinemaController';
import ModeLeverSoleil, { ModeLeverSoleilRef } from '../Background/ModeLeverSoleil';

// 🎬 CISCO: Interface pour l'expérience lever de soleil avec volets
interface SunriseExperienceProps {
  isActive: boolean;
  timerDuration?: number;
  children?: React.ReactNode;
}

// 🎬 CISCO: Composant principal de l'expérience lever de soleil
const SunriseExperience: React.FC<SunriseExperienceProps> = ({
  isActive,
  timerDuration = 260,
  children
}) => {
  const [experienceStarted, setExperienceStarted] = useState(false);
  const modeLeverSoleilRef = useRef<ModeLeverSoleilRef>(null);

  // 🎬 CISCO: Démarrage de l'expérience complète
  const handleExperienceStart = () => {
    // console.log('🎬 CISCO: Démarrage expérience lever de soleil');
    setExperienceStarted(true);
    
    // Activer l'audio et l'animation du ModeLeverSoleil
    if (modeLeverSoleilRef.current) {
      modeLeverSoleilRef.current.startExperience();
    }
  };

  // 🎬 CISCO: Fin de l'expérience
  const handleExperienceComplete = () => {
    // console.log('🎬 CISCO: Fin expérience lever de soleil');
    setExperienceStarted(false);
  };

  // 🎬 CISCO: Reset de l'expérience
  // const resetExperience = () => {
  //   console.log('🎬 CISCO: Reset expérience lever de soleil');
  //   setExperienceStarted(false);
  // };

  return (
    <>
      {/* 🔧 CISCO: SUPPRESSION VOLETS - ModeLeverSoleil direct sans présentation */}
      <ModeLeverSoleil
        ref={modeLeverSoleilRef}
        isActive={isActive}
        timerDuration={timerDuration}
        startPaused={false}
        onAudioUnlock={() => {}}
      />

      {/* 🎬 CISCO: Contenu additionnel */}
      {children}
    </>
  );
};

export default SunriseExperience;
