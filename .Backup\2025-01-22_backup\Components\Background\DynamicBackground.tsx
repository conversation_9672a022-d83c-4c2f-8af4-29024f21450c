// Déclaration TypeScript pour window.sunriseControls
declare global {
  interface Window {
    sunriseControls?: {
      getIntensity?: () => number;
    };
  }
}
import React, { useEffect, useRef } from 'react';
// 🌅 CISCO: NETTOYAGE RADICAL - SEULS MODULES NÉCESSAIRES
import <PERSON><PERSON><PERSON><PERSON> from './CloudLayer'; // 🔧 CISCO: NOUVEAU SYSTÈME NUAGES PROPRE
import SunriseExperience from '../Cinema/SunriseExperience';

// 🌅 CISCO: NETTOYAGE RADICAL - SEUL MODE AUTORISÉ

interface DynamicBackgroundProps {
  skyMode?: string;
  children?: React.ReactNode;
}

const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ 
  skyMode = 'leverSoleil', 
  children 
}) => {
  // 🌅 CISCO: RÉFÉRENCES MINIMALES
  const landscapeRef = useRef<HTMLDivElement>(null);
  // zoomTimelineRef supprimé - animation zoom désactivée

  // 🌅 CISCO: FONCTION ZOOM/DÉZOOM PAYSAGE SUPPRIMÉE
  // Animation de zoom désactivée pour éliminer le "rectangle mobile suspect"

  // 🌅 CISCO: INITIALISATION PAYSAGE PLUS SOMBRE AU DÉMARRAGE
  useEffect(() => {
    // CISCO: Le paysage DOIT être plus sombre au démarrage (même avec pleine lune)
    if (landscapeRef.current) {
      landscapeRef.current.style.filter = 'brightness(0.08)'; // Plus sombre au début
      // console.log('🌄 CISCO: Paysage initialisé PLUS SOMBRE (brightness 0.08)');
    }
  }, []);

  return (
    <div
      className="fixed inset-0 w-full h-full overflow-hidden"
      style={{
        backgroundColor: '#0B1426'
      }}
    >
      {/* 🌤️ CISCO: NOUVEAU SYSTÈME NUAGES PROPRE */}
      <CloudLayer skyMode={skyMode} isVisible={true} />

      {/* 🌅 CISCO: EXPÉRIENCE LEVER DE SOLEIL AVEC VOLETS CINÉMATOGRAPHIQUES */}
      <SunriseExperience
        isActive={skyMode === 'leverSoleil'}
        timerDuration={260}
      />

      {/* 🌅 CISCO: PAYSAGE FIXE (repère visuel, ne pas supprimer) */}
      <div
        ref={landscapeRef}
        className="fixed inset-0 w-full h-full bg-cover bg-center bg-no-repeat pointer-events-none"
        style={{
          backgroundImage: 'url(/Background.png)',
          backgroundPosition: 'center bottom -200px',
          backgroundSize: 'cover',
          zIndex: 10,
          transformOrigin: 'center center',
          willChange: 'transform, filter'
        }}
      />
      {/* Contenu principal scrollable */}
      <div
        className="relative main-scroll-content"
        style={{
          zIndex: 15,
          position: 'relative',
          height: '100vh',
          overflowY: 'auto',
          scrollbarWidth: 'none', // Firefox
          msOverflowStyle: 'none' // IE/Edge
        }}
      >
        {children}
      </div>
      {/* Masquage ascenseur pour Chrome/Safari/Edge */}
      <style dangerouslySetInnerHTML={{
        __html: `
          .main-scroll-content::-webkit-scrollbar {
            width: 0px;
            background: transparent;
          }
        `
      }} />
    </div>
  );
};

export default DynamicBackground;
