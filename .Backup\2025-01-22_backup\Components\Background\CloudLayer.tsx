import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';

// 🔧 CISCO: Interface pour un nuage - NOUVELLE ARCHITECTURE PROPRE
interface Cloud {
  id: number;
  x: number; // Position X initiale
  y: number; // Position Y aléatoire
  size: number; // Taille du nuage
  duration: number; // Durée de l'animation (vitesse)
  cloudNumber: number; // Numéro du nuage dans la collection (48-70)
  verticalDrift: number; // Dérive verticale naturelle
  opacity: number; // Opacité variable
  zIndex: number; // Profondeur pour effet de parallaxe
}

// Interface pour les props du composant
interface CloudLayerProps {
  skyMode: string;
  isVisible?: boolean; // Contrôle de visibilité
}

// 🌤️ CISCO: SYSTÈME FILTRES COULEUR - TEINTES NOCTURNES SANS ASSOMBRIR
const getCloudFilterForMode = (mode: string): string => {
  switch (mode) {
    case 'night':
      // 🌙 CISCO: Luminosité au MINIMUM + gris sépia
      return 'sepia(0.4) saturate(0.6) brightness(0.3)';
    case 'leverSoleil':
      return 'brightness(0.8) saturate(1.0) contrast(1.0) hue-rotate(0deg)';
    case 'dawn':
      // 🌅 CISCO: Fin de lune - AUCUN filtre = état naturel blanc
      return 'none';
    case 'sunrise':
      return 'brightness(0.9) saturate(1.4) contrast(1.1) hue-rotate(15deg) sepia(0.2)';
    case 'morning':
      // ☀️ CISCO: Lever de soleil - AUCUN filtre = blanc naturel
      return 'none';
    case 'midday':
      return 'brightness(1.2) saturate(0.9) contrast(0.95) hue-rotate(0deg)';
    case 'afternoon':
      return 'brightness(1.1) saturate(1.1) contrast(1.0) hue-rotate(10deg) sepia(0.05)';
    case 'sunset':
      return 'brightness(1.0) saturate(1.5) contrast(1.1) hue-rotate(20deg) sepia(0.3)';
    case 'dusk':
      return 'brightness(0.6) saturate(1.2) contrast(1.1) hue-rotate(12deg) sepia(0.15)';
    default:
      return 'brightness(1.0) saturate(1.0) contrast(1.0)';
  }
};

const CloudLayer: React.FC<CloudLayerProps> = ({ skyMode, isVisible = true }) => {
  // console.log('🌤️ CISCO: CloudLayer RENDU - skyMode:', skyMode, 'isVisible:', isVisible);

  const containerRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const cloudTransitionTimelineRef = useRef<gsap.core.Timeline | null>(null); // 🌙 CISCO: Timeline pour transition nuages
  const [clouds, setClouds] = useState<Cloud[]>([]);

  // 🔧 CISCO: Fonction pour générer les nuages - NOUVELLE COLLECTION HAUTE QUALITÉ
  const generateClouds = (): Cloud[] => {
    const newClouds: Cloud[] = [];
    const cloudCount = 20; // 🔧 CISCO: 20 nuages haute qualité

    // 🔧 CISCO: Collection corrigée - Nuages existants uniquement (48, 50-70)
    const availableCloudNumbers = [48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70];
    const remainingCloudNumbers = [...availableCloudNumbers];

    for (let i = 0; i < cloudCount; i++) {
      if (remainingCloudNumbers.length === 0) {
        // console.warn('⚠️ Plus de nuages uniques disponibles, arrêt de la génération');
        break;
      }

      const randomIndex = Math.floor(Math.random() * remainingCloudNumbers.length);
      const cloudNumber = remainingCloudNumbers[randomIndex];
      remainingCloudNumbers.splice(randomIndex, 1);

      // Tailles optimisées
      const sizeCategory = Math.random();
      let cloudSize;
      if (sizeCategory < 0.6) {
        cloudSize = 1.2 + Math.random() * 0.8; // 1.2x à 2.0x
      } else {
        cloudSize = 2.0 + Math.random() * 1.5; // 2.0x à 3.5x
      }

      const duration = 900; // 🔧 CISCO: 15 minutes - ULTRA LENT et contemplatif (réduit vitesse)
      const verticalDrift = (Math.random() - 0.5) * 30; // ±15% de dérive verticale
      const opacity = 1.0; // 100% opaque
      const zIndex = 9; // 🔧 CISCO: DERRIÈRE le paysage (z-index 10) - RÈGLE ABSOLUE

      newClouds.push({
        id: i,
        x: -10 + Math.random() * 130, // -10% à 120%
        y: 3 + Math.random() * 47, // 3% à 50%
        size: cloudSize,
        duration: duration,
        cloudNumber: cloudNumber,
        verticalDrift: verticalDrift,
        opacity: opacity,
        zIndex: zIndex
      });
    }

    // Triple mélange pour dispersion parfaite
    for (let shuffle = 0; shuffle < 3; shuffle++) {
      for (let i = newClouds.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newClouds[i], newClouds[j]] = [newClouds[j], newClouds[i]];
      }
    }


    return newClouds;
  };

  // 🔧 CISCO: NETTOYAGE COMPLET - DOM + GSAP
  const cleanupClouds = () => {
    // Nettoyer la timeline GSAP principale
    if (timelineRef.current) {
      timelineRef.current.kill();
      timelineRef.current = null;
    }

    // 🌙 CISCO: Nettoyer la timeline de transition des nuages
    if (cloudTransitionTimelineRef.current) {
      cloudTransitionTimelineRef.current.kill();
      cloudTransitionTimelineRef.current = null;
    }

    // Nettoyer le DOM
    if (containerRef.current) {
      containerRef.current.innerHTML = '';
    }

    // Nettoyer les styles CSS
    const style = document.querySelector('#cloud-animation-style');
    if (style && style.parentNode) {
      style.parentNode.removeChild(style);
    }


  };

  // 🔧 CISCO: INITIALISATION DES NUAGES - SEULEMENT au montage et changement de visibilité
  useEffect(() => {
    // console.log('🌤️ CISCO: useEffect INITIALISATION - isVisible:', isVisible);

    if (!isVisible || !containerRef.current) {
      // console.log('🌤️ CISCO: ARRÊT initialisation - conditions non remplies');
      return;
    }

    // console.log('🌤️ CISCO: NETTOYAGE avant création nuages');
    // Nettoyer avant de créer
    cleanupClouds();

    // Générer nouveaux nuages
    // console.log('🌤️ CISCO: GÉNÉRATION nouveaux nuages');
    const newClouds = generateClouds();
    setClouds(newClouds);
    // console.log(`🌤️ CISCO: ${newClouds.length} nuages générés et ajoutés au state`);

    // 🔧 CISCO: Créer les éléments DOM avec chargement progressif
    newClouds.forEach((cloud, index) => {
      // Délai progressif pour éviter saturation réseau
      setTimeout(() => {
        const imageSrc = `/Clouds/cloud_${cloud.cloudNumber}.png`;

        const cloudElement = document.createElement('div');
        cloudElement.className = 'cloud';
        cloudElement.setAttribute('data-cloud-element', 'true');
        cloudElement.setAttribute('data-cloud-id', cloud.id.toString());

        // 🌙 CISCO: NUAGES TOUJOURS SOMBRES AU DÉMARRAGE (même en leverSoleil)
        const initialFilter = skyMode === 'leverSoleil'
          ? getCloudFilterForMode('night') // Commencer sombre pour transition automatique
          : getCloudFilterForMode(skyMode); // Autres modes = filtre direct

        // console.log(`🌙 CISCO DEBUG: skyMode=${skyMode}, initialFilter=${initialFilter}`);

        cloudElement.style.cssText = `
          position: absolute;
          left: ${cloud.x}%;
          top: ${cloud.y}%;
          --cloud-scale: ${cloud.size};
          --vertical-drift: ${cloud.verticalDrift}%;
          pointer-events: none;
          z-index: ${cloud.zIndex};
          transform: translateX(-50%) translateY(-50%) scale(var(--cloud-scale));
          animation: cloud-drift-simple ${cloud.duration}s linear infinite;
          opacity: ${cloud.opacity};
          will-change: transform;
        `;

        const imgElement = document.createElement('img');
        imgElement.src = imageSrc;
        imgElement.alt = `Nuage ${cloud.cloudNumber}`;
        imgElement.className = 'cloud-image';

        const imageSize = Math.floor(80 + cloud.size * 50);

        imgElement.style.cssText = `
          width: ${imageSize}px;
          height: auto;
          user-select: none;
          transform: translateZ(0);
          backface-visibility: hidden;
          filter: ${initialFilter};
          transition: filter 15s ease-in-out;
        `;

        // console.log(`🌙 CISCO DEBUG: Nuage ${cloud.cloudNumber} créé avec filtre: ${initialFilter}`);

        // 🔧 CISCO: Gestion d'erreur de chargement image
        imgElement.onload = () => {
          // Réappliquer le filtre après chargement pour être sûr
          imgElement.style.filter = initialFilter;
          // console.log(`✅ Nuage ${cloud.cloudNumber} chargé avec filtre: ${initialFilter}`);
        };

        imgElement.onerror = () => {
          // console.error(`❌ Erreur chargement nuage ${cloud.cloudNumber}`);
        };

        cloudElement.appendChild(imgElement);
        if (containerRef.current) {
          containerRef.current.appendChild(cloudElement);
          // console.log(`🌤️ DEBUG: Nuage ${cloud.cloudNumber} ajouté au DOM - Position: ${cloud.x}%, ${cloud.y}% - Taille: ${cloud.size} - Z-index: ${cloud.zIndex}`);
        }
      }, index * 100); // 🔧 CISCO: Délai de 100ms entre chaque nuage
    });

    // Ajouter les styles CSS d'animation
    if (!document.querySelector('#cloud-animation-style')) {
      const style = document.createElement('style');
      style.id = 'cloud-animation-style';
      style.textContent = `
        @keyframes cloud-drift-simple {
          0% {
            transform: translateY(-50%) scale(var(--cloud-scale)) translateX(calc(-50% - 20vw));
          }
          100% {
            transform: translateY(-50%) scale(var(--cloud-scale)) translateX(calc(-50% + 120vw));
          }
        }

        .cloud {
          will-change: transform;
        }
        
        .cloud-image {
          display: block !important;
          opacity: 1 !important;
        }
      `;
      document.head.appendChild(style);
    }

    // Nettoyage au démontage
    return cleanupClouds;
  }, [skyMode, isVisible]); // 🔧 CISCO: RESTAURATION dépendances originales pour éviter erreur React

  // 🌙 CISCO: SYSTÈME TRANSITION NUAGES INTELLIGENT - Éclaircissement progressif après coucher lune
  useEffect(() => {
    if (!containerRef.current || !isVisible) return;

    // console.log('🌤️ CISCO: useEffect FILTRES - skyMode:', skyMode);

    // 🌙 CISCO: Mode leverSoleil = Transition automatique des nuages selon timeline lune
    if (skyMode === 'leverSoleil') {
      // Tuer l'ancienne timeline si elle existe
      if (cloudTransitionTimelineRef.current) {
        cloudTransitionTimelineRef.current.kill();
      }

      // Attendre un peu que les nuages soient créés, puis démarrer la timeline
      setTimeout(() => {
        const cloudImages = containerRef.current?.querySelectorAll('.cloud-image') as NodeListOf<HTMLImageElement>;
        // console.log(`🌙 CISCO: ${cloudImages?.length || 0} nuages trouvés pour transition automatique`);

        if (!cloudImages || cloudImages.length === 0) {
          console.warn('🌙 CISCO: AUCUN nuage trouvé - Timeline annulée');
          return;
        }

        // Créer timeline de transition des nuages synchronisée avec la lune
        cloudTransitionTimelineRef.current = gsap.timeline({ paused: false });
        const tl = cloudTransitionTimelineRef.current;

      // PHASE 1 (0s → 56.352s) : Nuages SOMBRES (mode nuit)
      tl.set(cloudImages, {
        filter: getCloudFilterForMode('night'), // brightness(0.3) - Nuages sombres
      }, 0);

      // console.log(`🌙 CISCO: PHASE 1 - ${cloudImages.length} nuages mis en mode SOMBRE`);

      // PHASE 2 (56.352s → 91.643s) : Suppression PROGRESSIVE de tous les effets jusqu'au lever soleil
      tl.to(cloudImages, {
        filter: 'none !important', // 🌙 CISCO: PNG brut - AUCUN effet, AUCUNE lumière
        duration: 45, // Transition plus longue pour être progressive
        ease: "power1.inOut",
        onStart: () => {}, // console.log('🌙 CISCO: DÉBUT transition nuages vers naturel à 56.352s'),
        onComplete: () => {
          // console.log('🌙 CISCO: FIN transition nuages - PNG naturel atteint à 91.643s');
          // 🔧 CISCO: Forcer TOUS les nuages à être PNG brut (sécurité)
          const allCloudImages = containerRef.current?.querySelectorAll('.cloud-image') as NodeListOf<HTMLImageElement>;
          allCloudImages?.forEach((img, index) => {
            img.style.filter = 'none';
            img.style.setProperty('filter', 'none', 'important');
            // console.log(`🌙 CISCO: Nuage ${index + 1} forcé en PNG BRUT - Aucune lumière`);
          });
        }
      }, 56.352);

        // console.log('🌙 CISCO: Timeline transition nuages créée - Coucher lune à 56.352s');
      }, 1000); // Attendre 1 seconde que les nuages soient créés

    } else {
      // 🔧 CISCO: Modes normaux - Application directe du filtre
      const cloudFilter = getCloudFilterForMode(skyMode);
      const cloudImages = containerRef.current.querySelectorAll('.cloud-image') as NodeListOf<HTMLImageElement>;
      // console.log(`🌤️ CISCO: ${cloudImages.length} images trouvées pour filtre direct:`, cloudFilter);

      cloudImages.forEach((imgElement) => {
        // Application directe du filtre CSS
        imgElement.style.filter = cloudFilter;
      });
    }
  }, [skyMode, isVisible]);

  return (
    <div
      ref={containerRef}
      data-cloud-layer="true"
      className="fixed inset-0 pointer-events-none overflow-hidden"
      style={{
        zIndex: 9, // 🔧 CISCO: DERRIÈRE le paysage (z-index 10) - RÈGLE ABSOLUE
        opacity: isVisible ? 1 : 0
      }}
    />
  );
};

export default CloudLayer;
