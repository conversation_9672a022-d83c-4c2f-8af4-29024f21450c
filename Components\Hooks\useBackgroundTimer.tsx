import { useEffect, useRef, useState, useCallback } from 'react';

// 🔄 Types pour le timer en arrière-plan
type TimerStatus = 'stopped' | 'running' | 'paused';
type TimerType = 'stopwatch' | 'countdown';

interface BackgroundTimerState {
  elapsedTime: number;
  remainingTime: number;
  status: TimerStatus;
  type: TimerType;
}

interface UseBackgroundTimerProps {
  onStop?: (elapsedTime: number) => void;
  onCountdownFinish?: () => void;
  enabled?: boolean;
}

// 🔄 Hook pour timer en arrière-plan avec Web Worker
export const useBackgroundTimer = ({
  onStop,
  onCountdownFinish,
  enabled = true
}: UseBackgroundTimerProps = {}) => {
  const workerRef = useRef<Worker | null>(null);
  const [workerReady, setWorkerReady] = useState(false);
  const intervalRef = useRef<number | null>(null);
  const startTimeRef = useRef<number>(0);
  const pausedTimeRef = useRef<number>(0);
  const [state, setState] = useState<BackgroundTimerState>({
    elapsedTime: 0,
    remainingTime: 0,
    status: 'stopped',
    type: 'stopwatch'
  });

  // 🚀 Initialisation du Web Worker
  useEffect(() => {
    if (!enabled) return;

    // 🔄 CISCO: Mode fallback direct - pas de Web Worker en développement
    console.log('🔄 Mode fallback activé - Timer sans Web Worker');
    setWorkerReady(true); // Activer directement le mode fallback


    // 🧹 Nettoyage
    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
        workerRef.current = null;
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [enabled, onStop, onCountdownFinish]);

  // 🔄 Système de fallback sans Web Worker
  const handleFallbackTimer = useCallback((type: string, data?: any) => {
    switch (type) {
      case 'start':
        if (data?.type === 'countdown') {
          startFallbackCountdown(data.duration);
        } else {
          startFallbackStopwatch();
        }
        break;
      case 'pause':
        pauseFallbackTimer();
        break;
      case 'resume':
        resumeFallbackTimer();
        break;
      case 'stop':
        stopFallbackTimer();
        break;
    }
  }, []);

  // 🎯 Fonction pour envoyer des messages au worker ou utiliser fallback
  const sendMessage = useCallback((type: string, data?: any) => {
    if (workerRef.current && workerReady) {
      console.log(`📤 Envoi message au worker: ${type}`, data);
      workerRef.current.postMessage({ type, data });
    } else {
      console.log(`🔄 Fallback timer pour: ${type}`, data);
      // 🔄 Fallback direct sans fonction intermédiaire
      switch (type) {
        case 'start':
          if (data?.type === 'countdown') {
            startFallbackCountdown(data.duration);
          } else {
            startFallbackStopwatch();
          }
          break;
        case 'pause':
          pauseFallbackTimer();
          break;
        case 'resume':
          resumeFallbackTimer();
          break;
        case 'stop':
          stopFallbackTimer();
          break;
      }
    }
  }, [workerReady]);



  // 🔄 Fallback: Démarrer chronomètre
  const startFallbackStopwatch = useCallback(() => {
    console.log('🔄 Fallback: Démarrage chronomètre');
    startTimeRef.current = Date.now() - pausedTimeRef.current;
    setState(prev => ({ ...prev, status: 'running', type: 'stopwatch' }));

    intervalRef.current = window.setInterval(() => {
      const elapsed = Date.now() - startTimeRef.current;
      setState(prev => ({ ...prev, elapsedTime: elapsed, status: 'running' }));
    }, 100);
  }, []);

  // 🔄 Fallback: Démarrer compte à rebours
  const startFallbackCountdown = useCallback((duration: number) => {
    console.log('🔄 Fallback: Démarrage compte à rebours:', duration);
    startTimeRef.current = Date.now();
    setState(prev => ({ ...prev, remainingTime: duration, status: 'running', type: 'countdown' }));

    intervalRef.current = window.setInterval(() => {
      const elapsed = Date.now() - startTimeRef.current;
      const remaining = Math.max(0, duration - elapsed);
      setState(prev => ({ ...prev, remainingTime: remaining, status: 'running' }));

      if (remaining <= 0) {
        stopFallbackTimer();
        // 🔊 Son de fin de compte à rebours
        if (typeof (window as any).playTimerSound === 'function') {
          (window as any).playTimerSound('countdown_finish');
        }
        if (onCountdownFinish) {
          onCountdownFinish();
        }
      }
    }, 100);
  }, [onCountdownFinish]);

  // 🔄 Fallback: Pause
  const pauseFallbackTimer = useCallback(() => {
    console.log('🔄 Fallback: Pause timer');
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setState(prev => ({ ...prev, status: 'paused' }));
    pausedTimeRef.current = Date.now() - startTimeRef.current;
  }, []);

  // 🔄 Fallback: Reprendre
  const resumeFallbackTimer = useCallback(() => {
    console.log('🔄 Fallback: Reprise timer, type:', state.type);
    if (state.type === 'stopwatch') {
      startFallbackStopwatch();
    } else {
      // Pour le countdown, on recalcule le temps restant
      const remainingTime = state.remainingTime;
      startFallbackCountdown(remainingTime);
    }
  }, [state.type, state.remainingTime, startFallbackStopwatch, startFallbackCountdown]);

  // 🔄 Fallback: Arrêter
  const stopFallbackTimer = useCallback(() => {
    console.log('🔄 Fallback: Arrêt timer');
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setState(prev => ({
      ...prev,
      status: 'stopped',
      elapsedTime: 0,
      remainingTime: 0
    }));
    pausedTimeRef.current = 0;
  }, []);



  // ▶️ Démarrer le chronomètre
  const startStopwatch = useCallback(() => {
    setState(prev => ({ ...prev, type: 'stopwatch', status: 'running' }));
    sendMessage('start', { type: 'stopwatch' });
    
    // 🔊 Son de démarrage
    if (typeof (window as any).playTimerSound === 'function') {
      (window as any).playTimerSound('start');
    }
  }, [sendMessage]);

  // ⏰ Démarrer le compte à rebours
  const startCountdown = useCallback((durationMs: number) => {
    setState(prev => ({ 
      ...prev, 
      type: 'countdown', 
      status: 'running',
      remainingTime: durationMs 
    }));
    sendMessage('start', { type: 'countdown', duration: durationMs });
    
    // 🔊 Son de démarrage
    if (typeof (window as any).playTimerSound === 'function') {
      (window as any).playTimerSound('start');
    }
  }, [sendMessage]);

  // ⏸️ Mettre en pause
  const pause = useCallback(() => {
    setState(prev => ({ ...prev, status: 'paused' }));
    sendMessage('pause');
    
    // 🔊 Son de pause
    if (typeof (window as any).playTimerSound === 'function') {
      (window as any).playTimerSound('pause');
    }
  }, [sendMessage]);

  // ▶️ Reprendre
  const resume = useCallback(() => {
    setState(prev => ({ ...prev, status: 'running' }));
    sendMessage('resume');
    
    // 🔊 Son de reprise
    if (typeof (window as any).playTimerSound === 'function') {
      (window as any).playTimerSound('resume');
    }
  }, [sendMessage]);

  // ⏹️ Arrêter
  const stop = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      status: 'stopped', 
      elapsedTime: 0, 
      remainingTime: 0 
    }));
    sendMessage('stop');
    
    // 🔊 Son d'arrêt
    if (typeof (window as any).playTimerSound === 'function') {
      (window as any).playTimerSound('stop');
    }
  }, [sendMessage]);

  // 📊 Obtenir le statut
  const getStatus = useCallback(() => {
    sendMessage('get_status');
  }, [sendMessage]);

  // 🔄 Pause forcée (pour compatibilité avec l'ancien système)
  const forcePause = useCallback(() => {
    if (state.status === 'running') {
      pause();
    }
  }, [state.status, pause]);

  return {
    // État
    elapsedTime: state.elapsedTime,
    remainingTime: state.remainingTime,
    status: state.status,
    type: state.type,
    
    // Actions
    startStopwatch,
    startCountdown,
    pause,
    resume,
    stop,
    forcePause,
    getStatus,
    
    // Utilitaires
    isWorkerSupported: !!workerRef.current
  };
};
